﻿# 编辑器设置备份脚本
# 备份Cursor和VSCode的设置、快捷键、代码片段、扩展等完整配置

param(
    [string]$BackupPath = "editors/backup"
)

# 设置控制台编码为UTF-8以支持中文输出
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "🚀 开始备份Cursor和VSCode设置..." -ForegroundColor Green

# 获取当前时间作为备份文件夹名称
$timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
$backupDir = Join-Path $BackupPath $timestamp

# 编辑器配置文件路径
$cursorUserPath = "$env:APPDATA\Cursor\User"
$vscodeUserPath = "$env:APPDATA\Code\User"

# 编辑器扩展路径
$cursorExtensionsPath = "$env:USERPROFILE\.cursor\extensions"
$vscodeExtensionsPath = "$env:USERPROFILE\.vscode\extensions"

# 检查编辑器配置目录是否存在
$cursorExists = Test-Path $cursorUserPath
$vscodeExists = Test-Path $vscodeUserPath

if (-not $cursorExists -and -not $vscodeExists) {
    Write-Host "❌ 错误：未找到任何编辑器配置目录" -ForegroundColor Red
    Write-Host "Cursor路径: $cursorUserPath" -ForegroundColor Yellow
    Write-Host "VSCode路径: $vscodeUserPath" -ForegroundColor Yellow
    Write-Host "请确保至少安装了Cursor或VSCode中的一个。" -ForegroundColor Yellow
    exit 1
}

if ($cursorExists) {
    Write-Host "✅ 发现Cursor配置目录: $cursorUserPath" -ForegroundColor Green
}
if ($vscodeExists) {
    Write-Host "✅ 发现VSCode配置目录: $vscodeUserPath" -ForegroundColor Green
}

# 创建备份目录
try {
    New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
    Write-Host "📁 创建备份目录: $backupDir" -ForegroundColor Cyan
    
    # 为每个编辑器创建子目录
    if ($cursorExists) {
        $cursorBackupDir = Join-Path $backupDir "cursor"
        New-Item -ItemType Directory -Path $cursorBackupDir -Force | Out-Null
    }
    if ($vscodeExists) {
        $vscodeBackupDir = Join-Path $backupDir "vscode"  
        New-Item -ItemType Directory -Path $vscodeBackupDir -Force | Out-Null
    }
} catch {
    Write-Host "❌ 错误：无法创建备份目录: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 定义要备份的文件和文件夹
$itemsToBackup = @(
    @{ Source = "settings.json"; Type = "File"; Description = "用户设置" },
    @{ Source = "keybindings.json"; Type = "File"; Description = "快捷键配置" },
    @{ Source = "snippets"; Type = "Directory"; Description = "代码片段" },
    @{ Source = "tasks.json"; Type = "File"; Description = "任务配置" },
    @{ Source = "launch.json"; Type = "File"; Description = "调试配置" }
)

# 扩展目录备份配置
$editorsExtensions = @()
if ($cursorExists) {
    $editorsExtensions += @{ 
        Name = "Cursor"; 
        UserPath = $cursorUserPath; 
        ExtensionsPath = $cursorExtensionsPath; 
        BackupDir = Join-Path $backupDir "cursor"
    }
}
if ($vscodeExists) {
    $editorsExtensions += @{ 
        Name = "VSCode"; 
        UserPath = $vscodeUserPath; 
        ExtensionsPath = $vscodeExtensionsPath; 
        BackupDir = Join-Path $backupDir "vscode"
    }
}

$successCount = 0
$totalCount = 0

# 备份每个编辑器的配置文件
foreach ($editor in $editorsExtensions) {
    Write-Host "`n🔧 开始备份 $($editor.Name) 配置..." -ForegroundColor Blue
    
    foreach ($item in $itemsToBackup) {
        $sourcePath = Join-Path $editor.UserPath $item.Source
        $destPath = Join-Path $editor.BackupDir $item.Source
        $totalCount++
        
        if (Test-Path $sourcePath) {
            try {
                if ($item.Type -eq "Directory") {
                    # 复制文件夹
                    Copy-Item -Path $sourcePath -Destination $destPath -Recurse -Force
                    $fileCount = (Get-ChildItem -Path $sourcePath -Recurse -File).Count
                    Write-Host "✅ [$($editor.Name)] 已备份 $($item.Description): $($item.Source) ($fileCount 个文件)" -ForegroundColor Green
                } else {
                    # 复制文件
                    Copy-Item -Path $sourcePath -Destination $destPath -Force
                    $fileSize = [math]::Round((Get-Item $sourcePath).Length / 1KB, 2)
                    Write-Host "✅ [$($editor.Name)] 已备份 $($item.Description): $($item.Source) ($fileSize KB)" -ForegroundColor Green
                }
                $successCount++
            } catch {
                Write-Host "⚠️  [$($editor.Name)] 备份 $($item.Description) 失败: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        } else {
            Write-Host "⏭️  [$($editor.Name)] 跳过 $($item.Description): 文件不存在" -ForegroundColor Gray
        }
    }
}

# 备份每个编辑器的已安装扩展列表
Write-Host "`n🔌 开始备份已安装扩展列表..." -ForegroundColor Blue
foreach ($editor in $editorsExtensions) {
    $sourcePath = $editor.ExtensionsPath
    $totalCount++
    
    Write-Host "`n📦 处理 $($editor.Name) 扩展..." -ForegroundColor Cyan
    
    if (Test-Path $sourcePath) {
        try {
            # 获取已安装的扩展目录
            $extensionDirs = Get-ChildItem -Path $sourcePath -Directory
            $extensionList = @()
            
            foreach ($extDir in $extensionDirs) {
                 $packageJsonPath = Join-Path $extDir.FullName "package.json"
                 if (Test-Path $packageJsonPath) {
                     try {
                         $packageJson = Get-Content $packageJsonPath -Raw -Encoding UTF8 | ConvertFrom-Json
                         
                         # 提取信息，带容错处理
                         $publisher = if ($packageJson.publisher) { $packageJson.publisher } else { "Unknown" }
                         $name = if ($packageJson.name) { $packageJson.name } else { "Unknown" }
                         $version = if ($packageJson.version) { $packageJson.version } else { "Unknown" }
                         $displayName = if ($packageJson.displayName) { $packageJson.displayName } else { $name }
                         $description = if ($packageJson.description) { $packageJson.description } else { "" }
                         
                         # 如果解析失败，尝试从目录名提取信息
                         if ($publisher -eq "Unknown" -or $name -eq "Unknown" -or $version -eq "Unknown") {
                             # 目录名格式通常是：publisher.name-version
                             if ($extDir.Name -match '^(.+?)\.(.+?)-(.+)$') {
                                 if ($publisher -eq "Unknown") { $publisher = $matches[1] }
                                 if ($name -eq "Unknown") { $name = $matches[2] }
                                 if ($version -eq "Unknown") { $version = $matches[3] }
                             }
                         }
                         
                         $extensionInfo = @{
                             name = $name
                             displayName = $displayName
                             version = $version
                             publisher = $publisher
                             description = $description
                             extensionId = "$publisher.$name"
                         }
                         $extensionList += $extensionInfo
                     } catch {
                         # 完全解析失败，尝试从目录名提取
                         $dirName = $extDir.Name
                         if ($dirName -match '^(.+?)\.(.+?)-(.+)$') {
                             $extensionList += @{
                                 name = $matches[2]
                                 displayName = $matches[2]
                                 version = $matches[3]
                                 publisher = $matches[1]
                                 description = "Parsed from directory name"
                                 extensionId = "$($matches[1]).$($matches[2])"
                             }
                         } else {
                             $extensionList += @{
                                 name = $dirName
                                 displayName = $dirName
                                 version = "Unknown"
                                 publisher = "Unknown"
                                 description = "Failed to parse package.json and directory name"
                                 extensionId = $dirName
                             }
                         }
                     }
                 }
            }
            
            if ($extensionList.Count -gt 0) {
                # 保存扩展列表到JSON文件
                $extensionsJsonPath = Join-Path $editor.BackupDir "extensions-list.json"
                $extensionList | ConvertTo-Json -Depth 3 | Out-File -FilePath $extensionsJsonPath -Encoding UTF8
                
                Write-Host "✅ [$($editor.Name)] 已备份扩展列表: $($extensionList.Count) 个扩展 (仅信息)" -ForegroundColor Green
                $successCount++
            } else {
                Write-Host "⏭️  [$($editor.Name)] 跳过扩展列表: 未找到已安装扩展" -ForegroundColor Gray
            }
        } catch {
            Write-Host "⚠️  [$($editor.Name)] 备份扩展列表失败: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⏭️  [$($editor.Name)] 跳过扩展列表: 扩展目录不存在" -ForegroundColor Gray
    }
}

# 创建备份信息文件
$backupInfo = @{
    BackupTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Editors = @()
    SuccessCount = $successCount
    TotalCount = $totalCount
}

# 为每个编辑器添加备份信息
foreach ($editor in $editorsExtensions) {
    $editorInfo = @{
        Name = $editor.Name
        Version = "Unknown"
        BackupItems = $itemsToBackup | ForEach-Object { 
            $sourcePath = Join-Path $editor.UserPath $_.Source
            @{
                Name = $_.Source
                Description = $_.Description
                Exists = Test-Path $sourcePath
                Type = $_.Type
            }
        }
        ExtensionInfo = @{
            ExtensionsPath = $editor.ExtensionsPath
            Exists = Test-Path $editor.ExtensionsPath
            ExtensionCount = if (Test-Path $editor.ExtensionsPath) { 
                (Get-ChildItem -Path $editor.ExtensionsPath -Directory).Count 
            } else { 0 }
        }
    }
    $backupInfo.Editors += $editorInfo
}

$backupInfoPath = Join-Path $backupDir "backup-info.json"
$backupInfo | ConvertTo-Json -Depth 3 | Out-File -FilePath $backupInfoPath -Encoding UTF8

Write-Host "`n📊 备份完成统计:" -ForegroundColor Magenta
Write-Host "   备份时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor White
Write-Host "   备份位置: $backupDir" -ForegroundColor White
Write-Host "   成功备份: $successCount/$totalCount 项" -ForegroundColor White

# 显示各编辑器备份情况
foreach ($editor in $editorsExtensions) {
    Write-Host "   $($editor.Name): 备份目录 $($editor.BackupDir)" -ForegroundColor White
}

if ($successCount -eq $totalCount) {
    Write-Host "`n🎉 所有编辑器配置文件和扩展列表备份成功！" -ForegroundColor Green
} elseif ($successCount -gt 0) {
    Write-Host "`n⚠️  部分编辑器配置文件和扩展列表备份成功，请检查上述输出。" -ForegroundColor Yellow
} else {
    Write-Host "`n❌ 备份失败，请检查错误信息。" -ForegroundColor Red
    exit 1
}


# 询问是否需要下载.vsix文件
Write-Host "`n" -NoNewline
$downloadChoice = Read-Host "🔽 是否需要从VSCode商店下载扩展.vsix文件进行完整备份？(y/N)"

if ($downloadChoice -eq 'y' -or $downloadChoice -eq 'Y' -or $downloadChoice -eq 'yes' -or $downloadChoice -eq 'Yes') {
    Write-Host "`n🌐 开始从VSCode商店下载扩展文件..." -ForegroundColor Blue
    
    # 为每个编辑器下载扩展文件
    foreach ($editor in $editorsExtensions) {
        Write-Host "`n📦 下载 $($editor.Name) 扩展文件..." -ForegroundColor Cyan
        
        # 创建扩展文件下载目录
        $extensionsDownloadDir = Join-Path $editor.BackupDir "extensions"
        if (-not (Test-Path $extensionsDownloadDir)) {
            New-Item -ItemType Directory -Path $extensionsDownloadDir -Force | Out-Null
        }
        
        # 读取扩展列表进行下载
        $extensionsJsonPath = Join-Path $editor.BackupDir "extensions-list.json"
        if (Test-Path $extensionsJsonPath) {
            try {
                $extensionList = Get-Content $extensionsJsonPath -Raw | ConvertFrom-Json
                $downloadSuccess = 0
                $downloadTotal = $extensionList.Count
                $failedExtensions = @()
                
                Write-Host "📦 准备下载 $($editor.Name) 的 $downloadTotal 个扩展..."
                
                $currentIndex = 0
                foreach ($extension in $extensionList) {
                    $currentIndex++
                    $extensionId = $extension.extensionId
                    $publisher = $extension.publisher
                    $name = $extension.name
                    $version = $extension.version
                    
                    if ($publisher -and $name) {
                        try {
                            # 构造下载URL（使用当前安装的版本，确保兼容性）
                            $downloadUrl = "https://marketplace.visualstudio.com/_apis/public/gallery/publishers/$publisher/vsextensions/$name/$version/vspackage"
                            $fileName = "$extensionId-$version.vsix"
                            $filePath = Join-Path $extensionsDownloadDir $fileName
                            
                            # 显示进度
                            $progress = [math]::Round(($currentIndex / $downloadTotal) * 100, 1)
                            Write-Progress -Activity "下载$($editor.Name)扩展文件" -Status "$currentIndex/$downloadTotal - $($extension.displayName)" -PercentComplete $progress
                            
                            # 调试信息：记录失败的URL（仅当下载失败时显示）
                            $debugUrl = $downloadUrl
                            
                            # 下载文件
                            Invoke-WebRequest -Uri $downloadUrl -OutFile $filePath -ErrorAction Stop
                            
                            # 检查文件是否存在
                            if (Test-Path $filePath) {
                                $downloadSuccess++
                            }
                        } catch {
                            # 记录失败的扩展和调试信息
                            $failedInfo = @{
                                Name = $extension.displayName
                                Id = $extensionId
                                Url = $debugUrl
                                Error = $_.Exception.Message
                            }
                            $failedExtensions += $failedInfo
                        }
                    }
                }
                
                Write-Progress -Activity "下载$($editor.Name)扩展文件" -Completed
                
                if ($downloadSuccess -eq $downloadTotal) {
                    Write-Host "🎉 [$($editor.Name)] 扩展下载完成！成功下载 $downloadSuccess/$downloadTotal 个扩展。" -ForegroundColor Green
                } elseif ($downloadSuccess -gt 0) {
                    Write-Host "⚠️  [$($editor.Name)] 扩展下载完成！成功下载 $downloadSuccess/$downloadTotal 个扩展。" -ForegroundColor Yellow
                    if ($failedExtensions.Count -gt 0) {
                        Write-Host "❌ [$($editor.Name)] 下载失败的扩展：" -ForegroundColor Red
                        foreach ($failedExt in $failedExtensions) {
                            Write-Host "   • $($failedExt.Name) ($($failedExt.Id))" -ForegroundColor Red
                        }
                    }
                } else {
                    Write-Host "❌ [$($editor.Name)] 扩展下载失败，请检查网络连接。" -ForegroundColor Red
                }
                
            } catch {
                Write-Host "❌ [$($editor.Name)] 读取扩展列表失败: $($_.Exception.Message)" -ForegroundColor Red
            }
        } else {
            Write-Host "❌ [$($editor.Name)] 找不到扩展列表文件，无法下载扩展。" -ForegroundColor Red
        }
    }
} else {
    Write-Host "`n⏭️  仅保留扩展列表信息，跳过扩展文件下载。" -ForegroundColor Gray
}
