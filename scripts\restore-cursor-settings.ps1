﻿# Cursor设置还原脚本
# 从备份文件还原Cursor的设置、快捷键、代码片段、扩展等完整配置

param(
    [string]$BackupPath,
    [switch]$Force
)

# 设置控制台编码为UTF-8以支持中文输出
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "🔄 开始还原Cursor设置..." -ForegroundColor Green

# 如果没有指定备份路径，则自动扫描并让用户选择
if (-not $BackupPath) {
    $backupRootDir = "cursor/backup"

    # 检查备份根目录是否存在
    if (-not (Test-Path $backupRootDir)) {
        Write-Host "❌ 错误：找不到备份根目录: $backupRootDir" -ForegroundColor Red
        Write-Host "💡 提示：请先运行 'npm run backup:cursor' 创建备份" -ForegroundColor Yellow
        exit 1
    }

    # 获取所有备份文件夹
    $backupFolders = Get-ChildItem -Path $backupRootDir -Directory | Sort-Object Name -Descending

    if ($backupFolders.Count -eq 0) {
        Write-Host "❌ 错误：在 $backupRootDir 中没有找到任何备份文件夹" -ForegroundColor Red
        Write-Host "💡 提示：请先运行 'npm run backup:cursor' 创建备份" -ForegroundColor Yellow
        exit 1
    }

    # 显示备份列表供用户选择
    Write-Host "`n📋 发现以下备份文件夹:" -ForegroundColor Cyan
    for ($i = 0; $i -lt $backupFolders.Count; $i++) {
        $folder = $backupFolders[$i]
        $backupInfoPath = Join-Path $folder.FullName "backup-info.json"

        if (Test-Path $backupInfoPath) {
            try {
                $backupInfo = Get-Content $backupInfoPath -Raw | ConvertFrom-Json
                $successInfo = "$($backupInfo.SuccessCount)/$($backupInfo.TotalCount) 项"
            } catch {
                $successInfo = "未知"
            }
        } else {
            $successInfo = "未知"
        }

        Write-Host "   [$($i + 1)] $($folder.Name) (成功: $successInfo)" -ForegroundColor White
    }

    # 让用户选择
    Write-Host ""
    do {
        $selection = Read-Host "请选择要还原的备份 (1-$($backupFolders.Count)) 或按 'q' 退出"

        if ($selection -eq 'q' -or $selection -eq 'Q') {
            Write-Host "❌ 用户取消操作。" -ForegroundColor Red
            exit 0
        }

        $selectedIndex = $null
        if ([int]::TryParse($selection, [ref]$selectedIndex)) {
            if ($selectedIndex -ge 1 -and $selectedIndex -le $backupFolders.Count) {
                $BackupPath = $backupFolders[$selectedIndex - 1].FullName
                Write-Host "✅ 已选择备份: $($backupFolders[$selectedIndex - 1].Name)" -ForegroundColor Green
                break
            }
        }

        Write-Host "⚠️  无效选择，请输入 1-$($backupFolders.Count) 之间的数字或 'q' 退出" -ForegroundColor Yellow
    } while ($true)
}

# 检查备份目录是否存在
if (-not (Test-Path $BackupPath)) {
    Write-Host "❌ 错误：找不到备份目录: $BackupPath" -ForegroundColor Red
    exit 1
}

# 检查备份信息文件
$backupInfoPath = Join-Path $BackupPath "backup-info.json"
if (-not (Test-Path $backupInfoPath)) {
    Write-Host "❌ 错误：找不到备份信息文件: $backupInfoPath" -ForegroundColor Red
    exit 1
}

# 读取备份信息
try {
    $backupInfo = Get-Content $backupInfoPath -Raw | ConvertFrom-Json
    Write-Host "📋 备份信息:" -ForegroundColor Cyan
    Write-Host "   备份时间: $($backupInfo.BackupTime)" -ForegroundColor White
    Write-Host "   成功项目: $($backupInfo.SuccessCount)/$($backupInfo.TotalCount)" -ForegroundColor White
} catch {
    Write-Host "❌ 错误：无法读取备份信息: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Cursor配置文件路径
$cursorUserPath = "$env:APPDATA\Cursor\User"

# 检查Cursor配置目录是否存在，不存在则创建
if (-not (Test-Path $cursorUserPath)) {
    Write-Host "📁 创建Cursor配置目录: $cursorUserPath" -ForegroundColor Cyan
    New-Item -ItemType Directory -Path $cursorUserPath -Force | Out-Null
}

# 确认还原操作
if (-not $Force) {
    Write-Host "`n⚠️  警告：此操作将覆盖当前的Cursor配置！" -ForegroundColor Yellow
    $confirm = Read-Host "是否继续还原？(y/N)"
    if ($confirm -ne 'y' -and $confirm -ne 'Y' -and $confirm -ne 'yes' -and $confirm -ne 'Yes') {
        Write-Host "❌ 用户取消操作。" -ForegroundColor Red
        exit 0
    }
}

$successCount = 0
$totalCount = 0
$successfulExtensions = 0
$failedExtensions = 0

# 还原配置文件
Write-Host "`n📂 开始还原配置文件..." -ForegroundColor Blue
foreach ($item in $backupInfo.BackupItems) {
    if (-not $item.Exists) {
        Write-Host "⏭️  跳过 $($item.Description): 备份中不存在" -ForegroundColor Gray
        continue
    }
    
    $sourcePath = Join-Path $BackupPath $item.Name
    $destPath = Join-Path $cursorUserPath $item.Name
    $totalCount++
    
    if (Test-Path $sourcePath) {
        try {
            # 如果目标文件/文件夹已存在，先备份
            if (Test-Path $destPath) {
                $backupName = "$($item.Name).backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
                $backupDestPath = Join-Path $cursorUserPath $backupName
                Move-Item -Path $destPath -Destination $backupDestPath -Force
                Write-Host "💾 已备份现有配置: $backupName" -ForegroundColor Yellow
            }
            
            if ($item.Type -eq "Directory") {
                # 复制文件夹
                Copy-Item -Path $sourcePath -Destination $destPath -Recurse -Force
                $fileCount = (Get-ChildItem -Path $sourcePath -Recurse -File).Count
                Write-Host "✅ 已还原 $($item.Description): $($item.Name) ($fileCount 个文件)" -ForegroundColor Green
            } else {
                # 复制文件
                Copy-Item -Path $sourcePath -Destination $destPath -Force
                $fileSize = [math]::Round((Get-Item $sourcePath).Length / 1KB, 2)
                Write-Host "✅ 已还原 $($item.Description): $($item.Name) ($fileSize KB)" -ForegroundColor Green
            }
            $successCount++
        } catch {
            Write-Host "⚠️  还原 $($item.Description) 失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️  跳过 $($item.Description): 备份文件不存在" -ForegroundColor Yellow
    }
}

# 还原扩展（如果有扩展文件）
$extensionsDir = Join-Path $BackupPath "extensions"
if (Test-Path $extensionsDir) {
    Write-Host "`n🔌 发现扩展文件，开始安装扩展..." -ForegroundColor Blue
    $extensionFiles = Get-ChildItem -Path $extensionsDir -Filter "*.vsix"
    
    if ($extensionFiles.Count -gt 0) {
        Write-Host "📦 找到 $($extensionFiles.Count) 个扩展文件" -ForegroundColor Cyan
        
        foreach ($extensionFile in $extensionFiles) {
            Write-Host "🔧 安装扩展: $($extensionFile.Name)" -ForegroundColor Cyan

            # 使用 Start-Process 来更好地捕获输出和错误
            $process = Start-Process -FilePath "cursor" -ArgumentList "--install-extension", $extensionFile.FullName -Wait -PassThru -NoNewWindow -RedirectStandardOutput "temp_output.txt" -RedirectStandardError "temp_error.txt"

            if ($process.ExitCode -eq 0) {
                Write-Host "✅ 扩展安装成功: $($extensionFile.BaseName)" -ForegroundColor Green
                $successfulExtensions++
            } else {
                # 读取错误信息
                $errorMessage = ""
                if (Test-Path "temp_error.txt") {
                    $errorMessage = Get-Content "temp_error.txt" -Raw
                }

                # 检查是否是兼容性问题
                if ($errorMessage -match "not compatible with VS Code") {
                    Write-Host "⚠️  扩展安装失败: $($extensionFile.BaseName) - 版本不兼容" -ForegroundColor Yellow
                    Write-Host "   💡 提示: 该扩展可能需要更新版本才能与当前Cursor兼容" -ForegroundColor Gray
                } elseif ($errorMessage -match "already installed") {
                    Write-Host "ℹ️  扩展已存在: $($extensionFile.BaseName) - 跳过安装" -ForegroundColor Cyan
                    $successfulExtensions++
                } else {
                    Write-Host "⚠️  扩展安装失败: $($extensionFile.BaseName)" -ForegroundColor Yellow
                    if ($errorMessage.Trim()) {
                        Write-Host "   错误详情: $($errorMessage.Trim())" -ForegroundColor Gray
                    }
                }
                $failedExtensions++
            }

            # 清理临时文件
            if (Test-Path "temp_output.txt") { Remove-Item "temp_output.txt" -Force }
            if (Test-Path "temp_error.txt") { Remove-Item "temp_error.txt" -Force }
        }

        # 显示扩展安装统计
        Write-Host "`n📊 扩展安装统计:" -ForegroundColor Magenta
        Write-Host "   成功安装: $successfulExtensions 个" -ForegroundColor Green
        Write-Host "   安装失败: $failedExtensions 个" -ForegroundColor Yellow
        Write-Host "   总计: $($extensionFiles.Count) 个" -ForegroundColor White
    }
} else {
    # 显示扩展列表信息
    $extensionsListPath = Join-Path $BackupPath "extensions-list.json"
    if (Test-Path $extensionsListPath) {
        Write-Host "`n📋 扩展列表信息 (需手动安装):" -ForegroundColor Blue
        try {
            $extensionList = Get-Content $extensionsListPath -Raw | ConvertFrom-Json
            Write-Host "   备份的扩展数量: $($extensionList.Count)" -ForegroundColor White
            Write-Host "   扩展列表文件: $extensionsListPath" -ForegroundColor White
            Write-Host "   💡 提示: 可以查看该文件获取扩展ID，然后手动安装" -ForegroundColor Yellow
        } catch {
            Write-Host "⚠️  无法读取扩展列表: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
}

Write-Host "`n📊 还原完成统计:" -ForegroundColor Magenta
Write-Host "   还原时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor White
Write-Host "   备份来源: $BackupPath" -ForegroundColor White
Write-Host "   成功还原: $successCount/$totalCount 项配置" -ForegroundColor White
if ($successfulExtensions -gt 0 -or $failedExtensions -gt 0) {
    Write-Host "   扩展安装: $successfulExtensions 成功, $failedExtensions 失败" -ForegroundColor White
}

if ($successCount -eq $totalCount -and $totalCount -gt 0) {
    Write-Host "`n🎉 所有配置文件还原成功！请重启Cursor以应用设置。" -ForegroundColor Green
} elseif ($successCount -gt 0) {
    Write-Host "`n⚠️  部分配置文件还原成功，请检查上述输出并重启Cursor。" -ForegroundColor Yellow
} else {
    Write-Host "`n❌ 还原失败，请检查错误信息。" -ForegroundColor Red
    exit 1
}

Write-Host "`n🔄 建议重启Cursor以确保所有设置生效。" -ForegroundColor Cyan